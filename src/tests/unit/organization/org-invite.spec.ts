// @ts-nocheck
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createMockSupabaseClient } from '$tests/mocks/supabase';

// Mock global fetch
global.fetch = vi.fn().mockImplementation(() => {
	return Promise.resolve({
		ok: true,
		json: () => Promise.resolve({ success: true }),
	});
});

// Mock SvelteKit fail and redirect before importing it
vi.mock('@sveltejs/kit', async () => {
	const actual = await vi.importActual('@sveltejs/kit');
	return {
		...actual,
		fail: vi.fn().mockImplementation((code, data) => ({ status: code, ...data })),
		redirect: vi.fn().mockImplementation((_code, location) => {
			throw new Error(`Redirect to ${location}`);
		}),
	};
});

// Mock sveltekit-flash-message/server
vi.mock('sveltekit-flash-message/server', () => {
	return {
		redirect: vi.fn().mockImplementation((location) => {
			throw new Error(`Redirect to ${location}`);
		}),
	};
});

// Mock the superforms modules
vi.mock('sveltekit-superforms/server', async () => {
	return {
		superValidate: vi.fn().mockImplementation(async (request, validator) => {
			// If request is an object with data property, treat it as pre-validated form
			if (request && typeof request === 'object' && 'data' in request) {
				return request;
			}

			// Otherwise assume it's a standard request
			const formData = await request?.formData?.();
			const data = Object.fromEntries(formData?.entries() || []);
			const validation = validator ? validator.safeParse(data) : { success: true, data };

			return {
				valid: validation.success,
				data: validation.success ? validation.data : data,
				errors: validation.success ? {} : validation.error?.flatten().fieldErrors || {},
			};
		}),
		message: vi.fn().mockImplementation((form, message) => {
			return { form, flashMessage: message };
		}),
	};
});

// Mock the superforms adapter
vi.mock('sveltekit-superforms/adapters', () => {
	return {
		zod: vi.fn().mockImplementation((schema) => schema),
	};
});

// Mock email sender
vi.mock('$lib/email', () => {
	return {
		sendEmail: vi.fn().mockResolvedValue({}),
	};
});

// Now import the mocked functions after all mocks are set up
import { superValidate, message } from 'sveltekit-superforms/server';

// Import the actions from the page being tested
import { actions, load } from '../../../routes/org/[org_name]/invite/+page.server';

// Mock the RPC function to support maybeSingle
const mockRpcWithMaybeSingle = (mockSupabase, mockData) => {
	mockSupabase.rpc = vi.fn().mockImplementation((func) => {
		return {
			maybeSingle: vi.fn().mockResolvedValue({
				data: mockData[func]?.data || null,
				error: mockData[func]?.error || null,
			}),
		};
	});

	// Add default from method with insert if not already defined
	if (!mockSupabase.from || typeof mockSupabase.from !== 'function') {
		mockSupabase.from = vi.fn().mockImplementation(() => {
			return {
				select: vi.fn().mockReturnThis(),
				eq: vi.fn().mockReturnThis(),
				single: vi.fn().mockResolvedValue({
					data: null,
					error: null,
				}),
				maybeSingle: vi.fn().mockResolvedValue({
					data: null,
					error: null,
				}),
				insert: vi.fn().mockResolvedValue({
					data: { id: 'new-id-123' },
					error: null,
				}),
			};
		});
	}

	return mockSupabase;
};

describe('Organization invite page', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe('Page load', () => {
		it('should return a form', async () => {
			// The load function just returns a form without any checks
			const result = await load();

			// Verify correct data is returned
			expect(result).toHaveProperty('form');
			expect(result.form).toHaveProperty('valid', true);
		});
	});

	describe('Form actions', () => {
		it('should return validation errors with invalid data', async () => {
			// Mock form data with invalid email
			const formData = new FormData();
			formData.append('email', 'invalid-email');
			formData.append('role', 'member');

			// Mock request
			const request = {
				formData: () => Promise.resolve(formData),
			};

			// Setup mock validation result
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: false,
				data: { email: 'invalid-email', role: 'member' },
				errors: { email: ['Invalid email address'] },
			});

			// Mock locals
			const mockSupabase = mockRpcWithMaybeSingle(createMockSupabaseClient(), {
				get_organization_by_name: {
					data: { org_id: 'org-123', name: 'Test Organization' },
				},
			});
			const locals = {
				supabase: mockSupabase,
				user: { id: 'user-123' },
			};

			// Run the invite action
			const result = await actions.default({
				request,
				locals,
				params: { org_name: 'test-org' },
				cookies: {},
				fetch: vi.fn(),
			});

			// Verify fail was called with correct parameters
			expect(result).toHaveProperty('status', 400);
			expect(result).toHaveProperty('form');
		});

		it('should check if user has admin access', async () => {
			// Mock form data
			const formData = new FormData();
			formData.append('email', '<EMAIL>');
			formData.append('role', 'member');

			// Mock request
			const request = {
				formData: () => Promise.resolve(formData),
			};

			// Setup mock validation result
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: { email: '<EMAIL>', role: 'member' },
			});

			// Mock non-admin response
			const mockSupabase = mockRpcWithMaybeSingle(createMockSupabaseClient(), {
				get_organization_by_name: {
					data: { org_id: 'org-123', name: 'Test Organization' },
				},
				current_user_has_entity_role: {
					data: false, // Not an admin
					error: null,
				},
			});

			const locals = {
				supabase: mockSupabase,
				user: { id: 'user-123' },
			};

			// Run the invite action
			const result = await actions.default({
				request,
				locals,
				params: { org_name: 'test-org' },
				cookies: {},
				fetch: vi.fn(),
			});

			// Verify access check failed
			expect(result).toHaveProperty('status', 403);
			expect(result.message).toContain('not authorized');
		});

		it('should handle adding an existing user to the organization', async () => {
			// Mock form data
			const formData = new FormData();
			formData.append('email', '<EMAIL>');
			formData.append('role', 'member');

			// Mock request
			const request = {
				formData: () => Promise.resolve(formData),
			};

			// Setup mock validation and responses
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: { email: '<EMAIL>', role: 'member' },
			});

			// Mock API responses for admin user, existing user that isn't a member yet
			const mockSupabase = createMockSupabaseClient();

			// Mock RPC calls
			mockSupabase.rpc = vi.fn().mockImplementation((func) => {
				if (func === 'get_organization_by_name') {
					return {
						maybeSingle: vi.fn().mockResolvedValue({
							data: { org_id: 'org-123', name: 'Test Organization' },
							error: null,
						}),
					};
				} else if (func === 'current_user_has_entity_role') {
					return {
						data: true, // User is an admin
						error: null,
					};
				}
				return {
					maybeSingle: vi.fn().mockResolvedValue({
						data: null,
						error: null,
					}),
				};
			});

			// Mock profile check and membership insert
			const mockProfileFrom = {
				select: vi.fn().mockReturnThis(),
				eq: vi.fn().mockReturnThis(),
				maybeSingle: vi.fn().mockResolvedValue({
					data: { user_id: 'existing-user-123', email: '<EMAIL>' },
					error: null,
				}),
			};

			const mockMembershipFrom = {
				select: vi.fn().mockReturnThis(),
				eq: vi.fn().mockReturnThis(),
				insert: vi.fn().mockResolvedValue({
					data: { membership_id: 'new-membership-123' },
					error: null,
				}),
			};

			mockSupabase.from = vi.fn().mockImplementation((table) => {
				if (table === 'profile') {
					return mockProfileFrom;
				} else if (table === 'membership') {
					return mockMembershipFrom;
				}
				return {
					select: vi.fn().mockReturnThis(),
					eq: vi.fn().mockReturnThis(),
					maybeSingle: vi.fn().mockResolvedValue({
						data: null,
						error: null,
					}),
				};
			});

			const locals = {
				supabase: mockSupabase,
				user: { id: 'admin-123' },
			};

			const mockFetch = vi.fn();

			// Mock the message implementation for this test
			vi.mocked(message).mockImplementationOnce((form, messageObj) => {
				return { form, flashMessage: messageObj };
			});

			// Run the action
			const result = await actions.default({
				request,
				locals,
				params: { org_name: 'test-org' },
				cookies: {},
				fetch: mockFetch,
			});

			// Verify user was added to organization
			expect(mockSupabase.from).toHaveBeenCalledWith('profile');
			expect(mockSupabase.from).toHaveBeenCalledWith('membership');
			expect(mockMembershipFrom.insert).toHaveBeenCalledWith({
				entity_type: 'organization',
				entity_id: 'org-123',
				user_id: 'existing-user-123',
				role: 'viewer', // 'member' role is mapped to 'viewer' in the new system
			});

			// Verify success message
			expect(result).toHaveProperty('form');
			expect(result).toHaveProperty('flashMessage');
			expect(result.flashMessage).toHaveProperty('text', 'User added to organization');
			expect(result.flashMessage).toHaveProperty('type', 'success');
		});

		it('should handle sending an invitation to a new user', async () => {
			// Mock form data
			const formData = new FormData();
			formData.append('email', '<EMAIL>');
			formData.append('role', 'admin');

			// Mock request
			const request = {
				formData: () => Promise.resolve(formData),
			};

			// Setup mock validation and responses
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: { email: '<EMAIL>', role: 'admin' },
			});

			// Mock API responses for admin user, non-existing user
			const mockSupabase = createMockSupabaseClient();

			// Mock RPC calls
			mockSupabase.rpc = vi.fn().mockImplementation((func) => {
				if (func === 'get_organization_by_name') {
					return {
						maybeSingle: vi.fn().mockResolvedValue({
							data: { org_id: 'org-123', name: 'Test Organization' },
							error: null,
						}),
					};
				} else if (func === 'current_user_has_entity_role') {
					return {
						data: true, // User is an admin
						error: null,
					};
				}
				return {
					maybeSingle: vi.fn().mockResolvedValue({
						data: null,
						error: null,
					}),
				};
			});

			// Mock profile check to return null (user doesn't exist)
			mockSupabase.from = vi.fn().mockImplementation((table) => {
				if (table === 'profile') {
					return {
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: null, // User doesn't exist
							error: null,
						}),
					};
				}
				return {
					select: vi.fn().mockReturnThis(),
					eq: vi.fn().mockReturnThis(),
					maybeSingle: vi.fn().mockResolvedValue({
						data: null,
						error: null,
					}),
				};
			});

			// Mock fetch to return success for the invite API
			const mockFetch = vi.fn().mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ success: true }),
			});

			// Mock the message implementation for this test
			vi.mocked(message).mockImplementationOnce((form, messageObj) => {
				return { form, flashMessage: messageObj };
			});

			const locals = {
				supabase: mockSupabase,
				user: { id: 'admin-123' },
			};

			// Run the action
			const result = await actions.default({
				request,
				locals,
				params: { org_name: 'test-org' },
				cookies: {},
				fetch: mockFetch,
			});

			// Verify profile was checked
			expect(mockSupabase.from).toHaveBeenCalledWith('profile');

			// Verify fetch was called with the right parameters
			expect(mockFetch).toHaveBeenCalledWith('/api/invites', {
				method: 'POST',
				body: expect.stringContaining('"resourceType":"organization"'),
			});

			// Verify success message
			expect(result).toHaveProperty('form');
			expect(result).toHaveProperty('flashMessage');
			expect(result.flashMessage).toHaveProperty('text', 'Invitation sent');
			expect(result.flashMessage).toHaveProperty('type', 'success');
		});

		it('should prevent re-inviting existing members', async () => {
			// Mock form data
			const formData = new FormData();
			formData.append('email', '<EMAIL>');
			formData.append('role', 'member');

			// Mock request
			const request = {
				formData: () => Promise.resolve(formData),
			};

			// Setup mock validation and responses
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: { email: '<EMAIL>', role: 'member' },
			});

			// Mock API responses for admin user with existing member
			const mockSupabase = createMockSupabaseClient();

			// Mock RPC calls
			mockSupabase.rpc = vi.fn().mockImplementation((func) => {
				if (func === 'get_organization_by_name') {
					return {
						maybeSingle: vi.fn().mockResolvedValue({
							data: { org_id: 'org-123', name: 'Test Organization' },
							error: null,
						}),
					};
				} else if (func === 'current_user_has_entity_role') {
					return {
						data: true, // User is an admin
						error: null,
					};
				}
				return {
					maybeSingle: vi.fn().mockResolvedValue({
						data: null,
						error: null,
					}),
				};
			});

			// Mock profile check and membership check
			mockSupabase.from = vi.fn().mockImplementation((table) => {
				if (table === 'profile') {
					return {
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: { user_id: 'existing-user-123', email: '<EMAIL>' },
							error: null,
						}),
					};
				} else if (table === 'membership') {
					return {
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						insert: vi.fn().mockResolvedValue({
							data: null,
							error: { code: '23505', message: 'duplicate key value violates unique constraint' },
						}),
					};
				}
				return {
					select: vi.fn().mockReturnThis(),
					eq: vi.fn().mockReturnThis(),
					maybeSingle: vi.fn().mockResolvedValue({
						data: null,
						error: null,
					}),
				};
			});

			const locals = {
				supabase: mockSupabase,
				user: { id: 'admin-123' },
			};

			// Mock the message implementation for this test
			vi.mocked(message).mockImplementationOnce((form, messageObj) => {
				return { form, flashMessage: messageObj };
			});

			// Run the invite action
			const result = await actions.default({
				request,
				locals,
				params: { org_name: 'test-org' },
				cookies: {},
				fetch: vi.fn(),
			});

			// Verify we get proper error
			expect(result).toHaveProperty('form');
			expect(result).toHaveProperty('flashMessage');
			expect(result.flashMessage).toHaveProperty('text', 'Something went wrong');
			expect(result.flashMessage).toHaveProperty('type', 'error');
		});

		it('should handle email sending failures', async () => {
			// Mock form data
			const formData = new FormData();
			formData.append('email', '<EMAIL>');
			formData.append('role', 'member');

			// Mock request
			const request = {
				formData: () => Promise.resolve(formData),
			};

			// Setup mock validation and responses
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: { email: '<EMAIL>', role: 'member' },
			});

			// Mock API responses for admin user, non-existing user
			const mockSupabase = createMockSupabaseClient();

			// Mock RPC calls
			mockSupabase.rpc = vi.fn().mockImplementation((func) => {
				if (func === 'get_organization_by_name') {
					return {
						maybeSingle: vi.fn().mockResolvedValue({
							data: { org_id: 'org-123', name: 'Test Organization' },
							error: null,
						}),
					};
				} else if (func === 'current_user_has_entity_role') {
					return {
						data: true, // User is an admin
						error: null,
					};
				}
				return {
					maybeSingle: vi.fn().mockResolvedValue({
						data: null,
						error: null,
					}),
				};
			});

			// Mock profile check to return null (user doesn't exist)
			mockSupabase.from = vi.fn().mockImplementation((table) => {
				if (table === 'profile') {
					return {
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: null, // User doesn't exist
							error: null,
						}),
					};
				}
				return {
					select: vi.fn().mockReturnThis(),
					eq: vi.fn().mockReturnThis(),
					maybeSingle: vi.fn().mockResolvedValue({
						data: null,
						error: null,
					}),
				};
			});

			// Mock fetch to return an error
			const mockFetch = vi.fn().mockResolvedValue({
				ok: false,
				status: 500,
				json: () => Promise.resolve({ error: 'Failed to send invitation' }),
			});

			// Mock the message implementation for this test
			vi.mocked(message).mockImplementationOnce((form, messageObj) => {
				return { form, flashMessage: messageObj };
			});

			const locals = {
				supabase: mockSupabase,
				user: { id: 'admin-123' },
			};

			// Run the invite action
			const result = await actions.default({
				request,
				locals,
				params: { org_name: 'test-org' },
				cookies: {},
				fetch: mockFetch,
			});

			// Verify error was returned
			expect(result).toHaveProperty('form');
			expect(result).toHaveProperty('flashMessage');
			expect(result.flashMessage).toHaveProperty(
				'text',
				'Failed to send invitation email. Please try again.',
			);
			expect(result.flashMessage).toHaveProperty('type', 'error');
		});
	});
});
